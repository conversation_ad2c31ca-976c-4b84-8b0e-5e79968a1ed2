import { vectorStore } from '../utils/vectorStore';
import { textExtractor } from '../utils/textExtractor';

export class DocumentService {
  async uploadDocument(file) {
    try {
      const text = await textExtractor.extract(file);
      const chunks = this.splitIntoChunks(text);
      
      const documentId = await this.saveDocumentMetadata(file);
      
      await vectorStore.addDocuments(chunks.map(chunk => ({
        pageContent: chunk,
        metadata: { documentId, fileName: file.name }
      })));

      return { success: true, documentId };
    } catch (error) {
      this.logger.error('Erro ao fazer upload do documento:', error);
      throw error;
    }
  }

  // Implementar outros métodos auxiliares...
}